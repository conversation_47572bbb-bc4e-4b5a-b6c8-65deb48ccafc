# Activity Tasks Deployment Checklist

## Pre-Deployment ✅

- [x] Updated task identifiers in `internal/model/task_identifier.go`
- [x] Added new task definitions to `TaskDefinitionRegistry`
- [x] Updated task seeder in `internal/service/activity_cashback/task_seeder.go`
- [x] Added new daily task: "Submit Market Research"
- [x] Added new community task: "Share Trading Screenshot"
- [x] Updated existing community tasks (Twitter links, verification methods)
- [x] Verified automatic initialization flow exists

## Post-Deployment Verification

### 1. Application Startup ✅
Check application logs for:
```
✅ "Initializing Activity Cashback System..."
✅ "Seeding initial Activity Cashback data"
✅ "Starting task seeding"
✅ "Task seeding completed successfully"
✅ "Activity Cashback System initialized successfully"
```

### 2. Database Verification ✅
Query database to verify tasks exist:
```sql
-- Check total task count
SELECT category_name, COUNT(*) as task_count 
FROM activity_tasks at
JOIN task_categories tc ON at.category_id = tc.id 
GROUP BY tc.name;

-- Expected results:
-- daily: 7 tasks
-- community: 7 tasks  
-- trading: 5 tasks

-- Check new tasks specifically
SELECT name, points, frequency, task_identifier 
FROM activity_tasks 
WHERE task_identifier IN ('MARKET_RESEARCH', 'SHARE_TRADING_SCREENSHOT');
```

### 3. API Verification ✅
Test GraphQL/REST endpoints:
```graphql
query GetTasks {
  activityTasks {
    id
    name
    points
    frequency
    taskIdentifier
    taskIcon
    buttonText
  }
}
```

### 4. Task Details Verification ✅

#### New Daily Task: Submit Market Research
- [ ] Name: "Submit Market Research"
- [ ] Points: 5
- [ ] Frequency: DAILY
- [ ] Task ID: MARKET_RESEARCH
- [ ] Icon: 📝
- [ ] Button: "submit"
- [ ] Verification: MANUAL

#### New Community Task: Share Trading Screenshot  
- [ ] Name: "Share Trading Screenshot"
- [ ] Points: 10
- [ ] Frequency: DAILY
- [ ] Task ID: SHARE_TRADING_SCREENSHOT
- [ ] Icon: 📸
- [ ] Button: "upload"
- [ ] Verification: MANUAL

#### Updated Community Tasks
- [ ] Follow Twitter: Link updated to XBITDEX_ZH
- [ ] Retweet/Like: Frequency changed to MANUAL
- [ ] Join Telegram: Button text "join"

### 5. UI/UX Verification ✅
- [ ] New tasks appear in task list
- [ ] Icons display correctly (📝, 📸)
- [ ] Button texts are appropriate
- [ ] Task descriptions are clear
- [ ] Points display correctly

### 6. Functional Testing ✅
- [ ] Daily check-in works
- [ ] Trading tasks trigger correctly
- [ ] Community tasks can be initiated
- [ ] New tasks can be completed (manual verification)
- [ ] Points are awarded correctly

## Troubleshooting

### If Tasks Don't Appear
1. Check application logs for errors
2. Verify database connection
3. Check if task categories exist first
4. Manually run reseed if needed:
   ```bash
   go run ./cmd/reseed-tasks -config=config.yaml
   ```

### If Duplicate Tasks Appear
- System should prevent duplicates automatically
- Check logs for "Task already exists, skipping" messages
- If duplicates exist, investigate task identification logic

### If Points/Frequencies Are Wrong
- Verify `TaskDefinitionRegistry` in `task_identifier.go`
- Check task seeder configuration
- May need to update existing tasks manually

## Success Criteria ✅

- [ ] All 19 tasks created successfully (7 daily + 7 community + 5 trading)
- [ ] New tasks have correct points and frequencies
- [ ] Updated tasks reflect requirement changes
- [ ] No application errors during startup
- [ ] Users can see and interact with new tasks
- [ ] Task completion flows work correctly

## Rollback Plan

If issues occur:
1. Check application logs for specific errors
2. Disable problematic tasks via admin panel
3. Fix issues in code and redeploy
4. Manual database cleanup if needed:
   ```sql
   DELETE FROM activity_tasks WHERE task_identifier IN ('MARKET_RESEARCH', 'SHARE_TRADING_SCREENSHOT');
   ```

---

**Note**: Since you deleted existing tasks from DB, the system will create all tasks fresh during deployment, ensuring clean state with updated configurations.
