# Infinite Agent Commission Fix

## 🐛 Vấn đ<PERSON> được báo cáo

**User ID:** `019913e5-a460-7ef1-b10b-65187271b271`

**Mô tả:** User chỉ trade meme nhưng trong bảng `infinite_agent_configs` lại có dữ liệu contract:
- `contract_total_fee_usd = 0.041745`
- `contract_paid_commission_usd = 0.0058443`  
- `contract_net_fee_usd = 0.0317262`

## 🔍 Nguyên nhân gốc rễ

### Logic hiện tại trong `calculateInfiniteAgentTreeCommission`:

1. **Lấy toàn bộ referral tree:**
   ```go
   var treeNodeUserIDs []uuid.UUID
   err = global.GVA_DB.Model(&model.InfiniteAgentTreeNode{}).
       Where("tree_id = ?", snapshot.ID).
       Pluck("user_id", &treeNodeUserIDs).Error
   ```

2. **Tính phí contract cho TẤT CẢ users trong tree:**
   ```go
   contractTotalFeeUSD, contractPaidCommissionUSD, err := t.calculateContractFeeBreakdown(treeNodeUserIDs)
   ```

3. **Query tổng hợp từ tất cả users:**
   ```sql
   SELECT COALESCE(SUM(COALESCE(hlt.build_fee, 0)), 0) as total_contract_fee
   FROM hyper_liquid_transactions hlt
   WHERE hlt.user_id IN (user1, user2, user3, ...) -- TẤT CẢ users trong tree
   ```

### Kết quả:
- User chỉ trade meme
- Nhưng **có users khác trong referral tree đã trade contract**
- Hệ thống tính tổng contract fees từ toàn bộ tree và gán cho infinite agent

## 🛠️ Giải pháp đã triển khai

### 1. Thêm function kiểm tra user activity:

```go
// checkUserHasContractActivity Check if a specific user has any contract trading activity
func (t *InfiniteAgentCommissionTask) checkUserHasContractActivity(userID uuid.UUID) (bool, error) {
    var count int64
    err := global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
        Where("user_id = ? AND build_fee > 0", userID).
        Count(&count).Error
    if err != nil {
        return false, fmt.Errorf("failed to check user contract activity: %w", err)
    }
    return count > 0, nil
}
```

### 2. Thêm function tính phí cho user cụ thể:

```go
// calculateContractFeeBreakdownForUser Calculate contract fee breakdown for a specific user only
func (t *InfiniteAgentCommissionTask) calculateContractFeeBreakdownForUser(userID uuid.UUID) (decimal.Decimal, decimal.Decimal, error) {
    // Query chỉ cho user cụ thể thay vì toàn bộ tree
    err := global.GVA_DB.Debug().Raw(`
        SELECT COALESCE(SUM(COALESCE(hlt.build_fee, 0)), 0) as total_contract_fee
        FROM hyper_liquid_transactions hlt
        WHERE hlt.user_id = ?
    `, userID).Scan(&contractTotalFeeResult).Error
    // ...
}
```

### 3. Logic điều kiện mới:

```go
// Check if the infinite agent user themselves has contract activity
userHasContractActivity, err := t.checkUserHasContractActivity(infiniteAgentUserID)

var contractTotalFeeUSD, contractPaidCommissionUSD decimal.Decimal
if userHasContractActivity {
    // User has contract activity - calculate for entire tree (current behavior)
    contractTotalFeeUSD, contractPaidCommissionUSD, err = t.calculateContractFeeBreakdown(treeNodeUserIDs)
} else {
    // User has no contract activity - only calculate for the user themselves (should be zero)
    contractTotalFeeUSD, contractPaidCommissionUSD, err = t.calculateContractFeeBreakdownForUser(infiniteAgentUserID)
}
```

## 📊 Kết quả mong đợi

### Trước khi fix:
- User chỉ trade meme nhưng có contract data từ referral tree
- `contract_total_fee_usd = 0.041745` (từ downline users)
- `contract_paid_commission_usd = 0.0058443`
- `contract_net_fee_usd = 0.0317262`

### Sau khi fix:
- User chỉ trade meme sẽ có contract data = 0
- `contract_total_fee_usd = 0`
- `contract_paid_commission_usd = 0`
- `contract_net_fee_usd = 0`
- Chỉ có meme data được lưu

## 🧪 Testing

### 1. Chạy test script:
```bash
# Kiểm tra dữ liệu hiện tại
psql -d your_database -f scripts/test_infinite_agent_fix.sql
```

### 2. Chạy unit tests:
```bash
go test ./internal/task/infinite/ -v
```

### 3. Test với user cụ thể:
```bash
# Chạy commission calculation cho user 019913e5-a460-7ef1-b10b-65187271b271
# Kiểm tra logs để xem logic mới hoạt động
```

## 🔄 Rollback Plan

Nếu cần rollback, chỉ cần comment out logic mới và sử dụng logic cũ:

```go
// OLD LOGIC (rollback):
contractTotalFeeUSD, contractPaidCommissionUSD, err := t.calculateContractFeeBreakdown(treeNodeUserIDs)

// NEW LOGIC (current):
// userHasContractActivity, err := t.checkUserHasContractActivity(infiniteAgentUserID)
// if userHasContractActivity { ... }
```

## 📝 Notes

1. **Backward Compatibility:** Logic mới vẫn tương thích với users có contract activity
2. **Performance:** Thêm 1 query để check user activity, impact minimal
3. **Logging:** Thêm detailed logs để debug và monitor
4. **Testing:** Comprehensive test coverage cho cả scenarios

## 🚀 Deployment

1. Deploy code changes
2. Monitor logs cho user `019913e5-a460-7ef1-b10b-65187271b271`
3. Verify contract fields = 0 cho meme-only users
4. Confirm meme data vẫn chính xác
